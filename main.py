import asyncio
import os
import uuid
import logging
from pathlib import Path
from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# Global asyncio queue for file processing tasks
file_queue = asyncio.Queue()

# Number of concurrent workers
NUM_WORKERS = 3

@app.get("/")
def read_root():
    return {"message": "Hello, FastAPI!"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...), user_id: str = None):
    """
    Upload endpoint that accepts files and enqueues processing tasks
    """
    try:
        # Create uploads directory if it doesn't exist
        upload_dir = Path("tmp/uploads")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename to avoid conflicts
        file_extension = Path(file.filename).suffix if file.filename else ""
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # Save the uploaded file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Create task data
        task_data = {
            "file_path": str(file_path),
            "original_filename": file.filename,
            "user_id": user_id,
            "file_size": len(content),
            "content_type": file.content_type
        }

        # Enqueue the task
        await file_queue.put(task_data)

        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "saved_as": unique_filename,
            "file_path": str(file_path),
            "user_id": user_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def process_file_task(task_data: dict):
    """
    Process a single file backup task
    """
    try:
        file_path = task_data["file_path"]
        original_filename = task_data["original_filename"]
        user_id = task_data["user_id"]

        logger.info(f"Processing file: {original_filename} for user: {user_id}")

        # Simulate backup processing (replace with actual backup logic)
        await asyncio.sleep(2)  # Simulate processing time

        logger.info(f"Successfully processed file: {original_filename}")

    except Exception as e:
        logger.error(f"Error processing file task: {str(e)}")


async def worker(worker_id: int):
    """
    Worker that continuously processes tasks from the queue
    """
    logger.info(f"Worker {worker_id} started")

    while True:
        try:
            # Get task from queue
            task_data = await file_queue.get()

            logger.info(f"Worker {worker_id} processing task for file: {task_data['original_filename']}")

            # Process the task
            await process_file_task(task_data)

            # Mark task as done
            file_queue.task_done()

        except Exception as e:
            logger.error(f"Worker {worker_id} error: {str(e)}")
            file_queue.task_done()


@app.on_event("startup")
async def startup_event():
    """
    Start worker tasks when the application starts
    """
    logger.info(f"Starting {NUM_WORKERS} workers")

    # Start multiple worker tasks
    for i in range(NUM_WORKERS):
        asyncio.create_task(worker(i + 1))

    logger.info("All workers started successfully")


@app.get("/queue/status")
async def get_queue_status():
    """
    Get current queue status
    """
    return {
        "queue_size": file_queue.qsize(),
        "workers": NUM_WORKERS
    }
